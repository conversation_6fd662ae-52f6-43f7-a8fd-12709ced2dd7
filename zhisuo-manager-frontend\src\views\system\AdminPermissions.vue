<template>
  <div class="admin-permissions">
    <div class="page-header">
      <h3 class="page-title">管理员与权限</h3>
      <p class="page-subtitle">管理系统管理员账号及其权限配置</p>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加管理员
        </el-button>
      </div>
    </div>

    <!-- 权限标签页 -->
    <div class="permission-tabs">
      <div class="tab-nav">
        <div 
          v-for="tab in permissionTabs" 
          :key="tab.key"
          :class="['tab-item', { active: activePermissionTab === tab.key }]"
          @click="activePermissionTab = tab.key"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="table-controls">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索管理员"
          prefix-icon="Search"
          clearable
          style="width: 300px;"
        />
      </div>
      <div class="filter-controls">
        <el-button icon="Sort" text>排序</el-button>
        <el-button icon="Filter" text>筛选</el-button>
      </div>
    </div>

    <!-- 管理员列表表格 -->
    <div class="admin-table">
      <el-table 
        :data="filteredAdminList" 
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#374151' }"
      >
        <el-table-column prop="adminId" label="管理员ID" width="100" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱/用户名" width="200" />
        <el-table-column prop="phone" label="手机号" width="140" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">{{ row.role }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" text @click="editAdmin(row)">编辑</el-button>
            <el-button type="danger" text @click="deleteAdmin(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑管理员对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingAdmin ? '编辑管理员' : '添加管理员'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="adminForm" :rules="adminRules" ref="adminFormRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="adminForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="adminForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="adminForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="超级管理员" value="超级管理员" />
            <el-option label="普通管理员" value="普通管理员" />
            <el-option label="内容管理员" value="内容管理员" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="adminForm.status">
            <el-radio label="正常">正常</el-radio>
            <el-radio label="禁用">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!editingAdmin" label="初始密码" prop="password">
          <el-input v-model="adminForm.password" type="password" placeholder="请输入初始密码" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAdmin" :loading="saving">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Sort, Filter } from '@element-plus/icons-vue'

// 权限标签页
const permissionTabs = [
  { key: 'admin', label: '管理员列表' },
  { key: 'role', label: '角色管理' },
  { key: 'permission', label: '权限配置' }
]

const activePermissionTab = ref('admin')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const showAddDialog = ref(false)
const editingAdmin = ref(null)
const saving = ref(false)
const adminFormRef = ref()

// 管理员表单
const adminForm = reactive({
  name: '',
  email: '',
  phone: '',
  role: '',
  status: '正常',
  password: ''
})

// 表单验证规则
const adminRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [{ required: true, message: '请输入初始密码', trigger: 'blur' }]
}

// 管理员列表数据
const adminList = ref([
  {
    adminId: 'A001',
    name: '张三',
    email: '<EMAIL>',
    phone: '19070873408',
    role: '超级管理员',
    status: '正常',
    lastLoginTime: '2023-05-15 09:30:22'
  },
  {
    adminId: 'A002',
    name: '李四',
    email: '<EMAIL>',
    phone: '18976987543',
    role: '普通管理员',
    status: '正常',
    lastLoginTime: '2023-06-22 14:18:36'
  },
  {
    adminId: 'A003',
    name: '王五',
    email: '<EMAIL>',
    phone: '15878908543',
    role: '超级管理员',
    status: '正常',
    lastLoginTime: '2023-07-08 11:42:15'
  },
  {
    adminId: 'A004',
    name: '赵六',
    email: '<EMAIL>',
    phone: '18578038764',
    role: '普通管理员',
    status: '禁用',
    lastLoginTime: '2023-08-30 16:05:47'
  },
  {
    adminId: 'A005',
    name: '钱七',
    email: '<EMAIL>',
    phone: '15697868805',
    role: '超级管理员',
    status: '正常',
    lastLoginTime: '2023-09-12 10:24:33'
  }
])

// 过滤后的管理员列表
const filteredAdminList = computed(() => {
  let filtered = adminList.value
  if (searchKeyword.value) {
    filtered = filtered.filter(admin => 
      admin.name.includes(searchKeyword.value) ||
      admin.email.includes(searchKeyword.value) ||
      admin.phone.includes(searchKeyword.value)
    )
  }
  totalCount.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.slice(start, start + pageSize.value)
})

// 获取角色标签类型
const getRoleTagType = (role) => {
  switch (role) {
    case '超级管理员': return 'danger'
    case '普通管理员': return 'primary'
    case '内容管理员': return 'success'
    default: return 'info'
  }
}

// 编辑管理员
const editAdmin = (admin) => {
  editingAdmin.value = admin
  Object.assign(adminForm, admin)
  showAddDialog.value = true
}

// 删除管理员
const deleteAdmin = async (admin) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除管理员 "${admin.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = adminList.value.findIndex(item => item.adminId === admin.adminId)
    if (index > -1) {
      adminList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 保存管理员
const saveAdmin = async () => {
  if (!adminFormRef.value) return
  
  try {
    await adminFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingAdmin.value) {
      // 编辑模式
      const index = adminList.value.findIndex(item => item.adminId === editingAdmin.value.adminId)
      if (index > -1) {
        adminList.value[index] = { ...adminForm, adminId: editingAdmin.value.adminId }
      }
      ElMessage.success('编辑成功')
    } else {
      // 添加模式
      const newAdmin = {
        ...adminForm,
        adminId: 'A' + String(adminList.value.length + 1).padStart(3, '0'),
        lastLoginTime: '从未登录'
      }
      adminList.value.push(newAdmin)
      ElMessage.success('添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingAdmin.value = null
  Object.assign(adminForm, {
    name: '',
    email: '',
    phone: '',
    role: '',
    status: '正常',
    password: ''
  })
  adminFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  totalCount.value = adminList.value.length
})
</script>

<style lang="scss" scoped>
.admin-permissions {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }
    
    .page-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
  
  .permission-tabs {
    margin-bottom: 24px;
    
    .tab-nav {
      display: flex;
      border-bottom: 1px solid #e9ecef;
      
      .tab-item {
        padding: 12px 20px;
        cursor: pointer;
        font-size: 14px;
        color: #6b7280;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;
        
        &:hover {
          color: #722ED1;
        }
        
        &.active {
          color: #722ED1;
          border-bottom-color: #722ED1;
          font-weight: 500;
        }
      }
    }
  }
  
  .table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .filter-controls {
      display: flex;
      gap: 8px;
    }
  }
  
  .admin-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    
    .pagination-wrapper {
      padding: 16px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
