<template>
  <div class="security-config">
    <div class="config-section">
      <h3 class="section-title">
        <el-icon><Lock /></el-icon>
        密码策略
      </h3>
      <div class="config-grid">
        <div class="config-item">
          <label class="config-label">最小密码长度</label>
          <el-input-number 
            v-model="passwordPolicy.minLength" 
            :min="6" 
            :max="20"
            class="config-input"
          />
          <span class="config-desc">密码最少需要包含的字符数</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">密码复杂度</label>
          <el-checkbox-group v-model="passwordPolicy.complexity" class="config-input">
            <el-checkbox label="uppercase">包含大写字母</el-checkbox>
            <el-checkbox label="lowercase">包含小写字母</el-checkbox>
            <el-checkbox label="numbers">包含数字</el-checkbox>
            <el-checkbox label="symbols">包含特殊字符</el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="config-item">
          <label class="config-label">密码有效期</label>
          <el-input-number 
            v-model="passwordPolicy.expireDays" 
            :min="0" 
            :max="365"
            class="config-input"
          />
          <span class="config-desc">密码过期天数，0表示永不过期</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">密码重试次数</label>
          <el-input-number 
            v-model="passwordPolicy.maxRetries" 
            :min="3" 
            :max="10"
            class="config-input"
          />
          <span class="config-desc">密码错误后锁定账户的最大尝试次数</span>
        </div>
      </div>
    </div>

    <div class="config-section">
      <h3 class="section-title">
        <el-icon><Shield /></el-icon>
        登录安全
      </h3>
      <div class="config-grid">
        <div class="config-item">
          <label class="config-label">会话超时</label>
          <el-input-number 
            v-model="loginSecurity.sessionTimeout" 
            :min="30" 
            :max="1440"
            class="config-input"
          />
          <span class="config-desc">用户无操作自动退出时间（分钟）</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">双因子认证</label>
          <el-switch 
            v-model="loginSecurity.twoFactorAuth"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">启用后需要手机验证码或邮箱验证</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">IP白名单</label>
          <el-switch 
            v-model="loginSecurity.ipWhitelist"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">只允许指定IP地址登录</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">登录验证码</label>
          <el-switch 
            v-model="loginSecurity.captcha"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">登录时需要输入图形验证码</span>
        </div>
      </div>
    </div>

    <div class="config-section">
      <h3 class="section-title">
        <el-icon><Warning /></el-icon>
        安全监控
      </h3>
      <div class="config-grid">
        <div class="config-item">
          <label class="config-label">异常登录检测</label>
          <el-switch 
            v-model="securityMonitor.abnormalLogin"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">检测异常时间、地点的登录行为</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">操作日志记录</label>
          <el-switch 
            v-model="securityMonitor.operationLog"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">记录用户的重要操作行为</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">安全事件通知</label>
          <el-checkbox-group v-model="securityMonitor.notifications" class="config-input">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="system">系统通知</el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="config-item">
          <label class="config-label">日志保留期限</label>
          <el-input-number 
            v-model="securityMonitor.logRetentionDays" 
            :min="30" 
            :max="365"
            class="config-input"
          />
          <span class="config-desc">安全日志保留天数</span>
        </div>
      </div>
    </div>

    <div class="config-section">
      <h3 class="section-title">
        <el-icon><Key /></el-icon>
        数据加密
      </h3>
      <div class="config-grid">
        <div class="config-item">
          <label class="config-label">数据传输加密</label>
          <el-switch 
            v-model="dataEncryption.httpsOnly"
            active-text="强制HTTPS"
            inactive-text="允许HTTP"
            class="config-input"
          />
          <span class="config-desc">强制使用HTTPS协议传输数据</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">数据库加密</label>
          <el-switch 
            v-model="dataEncryption.databaseEncryption"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">对敏感数据进行数据库级加密</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">文件存储加密</label>
          <el-switch 
            v-model="dataEncryption.fileEncryption"
            active-text="启用"
            inactive-text="禁用"
            class="config-input"
          />
          <span class="config-desc">对上传文件进行加密存储</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">加密算法</label>
          <el-select v-model="dataEncryption.algorithm" class="config-input">
            <el-option label="AES-256" value="AES-256" />
            <el-option label="AES-128" value="AES-128" />
            <el-option label="RSA-2048" value="RSA-2048" />
          </el-select>
          <span class="config-desc">选择数据加密使用的算法</span>
        </div>
      </div>
    </div>

    <div class="config-section">
      <h3 class="section-title">
        <el-icon><View /></el-icon>
        访问控制
      </h3>
      <div class="config-grid">
        <div class="config-item">
          <label class="config-label">API访问限制</label>
          <el-input-number 
            v-model="accessControl.apiRateLimit" 
            :min="100" 
            :max="10000"
            class="config-input"
          />
          <span class="config-desc">每分钟API调用次数限制</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">跨域访问</label>
          <el-switch 
            v-model="accessControl.corsEnabled"
            active-text="允许"
            inactive-text="禁止"
            class="config-input"
          />
          <span class="config-desc">是否允许跨域请求</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">文件上传限制</label>
          <el-input-number 
            v-model="accessControl.maxFileSize" 
            :min="1" 
            :max="100"
            class="config-input"
          />
          <span class="config-desc">单个文件最大上传大小（MB）</span>
        </div>
        
        <div class="config-item">
          <label class="config-label">允许的文件类型</label>
          <el-input 
            v-model="accessControl.allowedFileTypes" 
            placeholder="jpg,png,pdf,doc"
            class="config-input"
          />
          <span class="config-desc">用逗号分隔的文件扩展名</span>
        </div>
      </div>
    </div>

    <div class="config-actions">
      <el-button type="primary" @click="saveConfig" :loading="saving">
        <el-icon><Check /></el-icon>
        保存配置
      </el-button>
      <el-button @click="resetConfig">
        <el-icon><RefreshLeft /></el-icon>
        重置配置
      </el-button>
      <el-button type="warning" @click="testSecurity">
        <el-icon><Monitor /></el-icon>
        安全测试
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock, Shield, Warning, Key, View, Check, RefreshLeft, Monitor } from '@element-plus/icons-vue'

// 密码策略配置
const passwordPolicy = reactive({
  minLength: 8,
  complexity: ['lowercase', 'numbers'],
  expireDays: 90,
  maxRetries: 5
})

// 登录安全配置
const loginSecurity = reactive({
  sessionTimeout: 120,
  twoFactorAuth: false,
  ipWhitelist: false,
  captcha: true
})

// 安全监控配置
const securityMonitor = reactive({
  abnormalLogin: true,
  operationLog: true,
  notifications: ['email', 'system'],
  logRetentionDays: 90
})

// 数据加密配置
const dataEncryption = reactive({
  httpsOnly: true,
  databaseEncryption: false,
  fileEncryption: false,
  algorithm: 'AES-256'
})

// 访问控制配置
const accessControl = reactive({
  apiRateLimit: 1000,
  corsEnabled: false,
  maxFileSize: 10,
  allowedFileTypes: 'jpg,png,gif,pdf,doc,docx,xls,xlsx'
})

const saving = ref(false)

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('安全配置保存成功')
  } catch (error) {
    ElMessage.error('安全配置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置配置
const resetConfig = () => {
  // 重置为默认值
  passwordPolicy.minLength = 8
  passwordPolicy.complexity = ['lowercase', 'numbers']
  passwordPolicy.expireDays = 90
  passwordPolicy.maxRetries = 5
  
  loginSecurity.sessionTimeout = 120
  loginSecurity.twoFactorAuth = false
  loginSecurity.ipWhitelist = false
  loginSecurity.captcha = true
  
  securityMonitor.abnormalLogin = true
  securityMonitor.operationLog = true
  securityMonitor.notifications = ['email', 'system']
  securityMonitor.logRetentionDays = 90
  
  dataEncryption.httpsOnly = true
  dataEncryption.databaseEncryption = false
  dataEncryption.fileEncryption = false
  dataEncryption.algorithm = 'AES-256'
  
  accessControl.apiRateLimit = 1000
  accessControl.corsEnabled = false
  accessControl.maxFileSize = 10
  accessControl.allowedFileTypes = 'jpg,png,gif,pdf,doc,docx,xls,xlsx'
  
  ElMessage.success('配置已重置为默认值')
}

// 安全测试
const testSecurity = async () => {
  try {
    ElMessage.info('正在进行安全测试...')
    // 模拟安全测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('安全测试通过，系统配置安全')
  } catch (error) {
    ElMessage.error('安全测试失败，请检查配置')
  }
}
</script>

<style lang="scss" scoped>
.security-config {
  .config-section {
    margin-bottom: 32px;
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        color: #722ED1;
      }
    }
    
    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 24px;
      
      .config-item {
        display: flex;
        flex-direction: column;
        
        .config-label {
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
        
        .config-input {
          margin-bottom: 4px;
        }
        
        .config-desc {
          font-size: 12px;
          color: #6b7280;
          line-height: 1.4;
        }
      }
    }
  }
  
  .config-actions {
    margin-top: 32px;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .el-checkbox {
    margin-right: 0;
  }
}
</style>
