<template>
  <div class="system-logs">
    <div class="logs-header">
      <h3 class="page-title">系统日志</h3>
      <p class="page-subtitle">查看和管理系统运行日志、操作记录和错误信息</p>
    </div>

    <!-- 日志类型标签页 -->
    <div class="log-tabs">
      <div class="tab-nav">
        <div 
          v-for="tab in logTabs" 
          :key="tab.key"
          :class="['tab-item', { active: activeLogTab === tab.key }]"
          @click="activeLogTab = tab.key"
        >
          <el-icon>
            <component :is="tab.icon" />
          </el-icon>
          {{ tab.label }}
          <el-badge v-if="tab.count" :value="tab.count" class="tab-badge" />
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="log-filters">
      <div class="filter-left">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px;"
        />
        
        <el-select v-model="selectedLevel" placeholder="日志级别" style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="信息" value="info" />
          <el-option label="警告" value="warning" />
          <el-option label="错误" value="error" />
          <el-option label="调试" value="debug" />
        </el-select>
        
        <el-select v-model="selectedModule" placeholder="模块" style="width: 150px;">
          <el-option label="全部模块" value="" />
          <el-option label="用户管理" value="user" />
          <el-option label="文章管理" value="article" />
          <el-option label="系统设置" value="system" />
          <el-option label="安全认证" value="auth" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索日志内容"
          prefix-icon="Search"
          clearable
          style="width: 300px;"
        />
        <el-button type="primary" @click="refreshLogs">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportLogs">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="log-table">
      <el-table 
        :data="filteredLogs" 
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#374151' }"
        row-key="id"
        :default-sort="{ prop: 'timestamp', order: 'descending' }"
      >
        <el-table-column prop="timestamp" label="时间" width="180" sortable>
          <template #default="{ row }">
            <span class="log-time">{{ formatTime(row.timestamp) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)" size="small">
              {{ getLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="模块" width="120">
          <template #default="{ row }">
            <span class="log-module">{{ getModuleText(row.module) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="user" label="用户" width="120">
          <template #default="{ row }">
            <span class="log-user">{{ row.user || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="action" label="操作" width="150">
          <template #default="{ row }">
            <span class="log-action">{{ row.action }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="message" label="详细信息" min-width="200">
          <template #default="{ row }">
            <div class="log-message">
              <p class="message-text">{{ row.message }}</p>
              <el-button 
                v-if="row.details" 
                type="text" 
                size="small" 
                @click="showLogDetails(row)"
              >
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="ip" label="IP地址" width="130">
          <template #default="{ row }">
            <span class="log-ip">{{ row.ip || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="日志详情"
      width="800px"
    >
      <div v-if="selectedLog" class="log-details">
        <div class="detail-item">
          <label>时间：</label>
          <span>{{ formatTime(selectedLog.timestamp) }}</span>
        </div>
        <div class="detail-item">
          <label>级别：</label>
          <el-tag :type="getLevelTagType(selectedLog.level)">
            {{ getLevelText(selectedLog.level) }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>模块：</label>
          <span>{{ getModuleText(selectedLog.module) }}</span>
        </div>
        <div class="detail-item">
          <label>用户：</label>
          <span>{{ selectedLog.user || '-' }}</span>
        </div>
        <div class="detail-item">
          <label>操作：</label>
          <span>{{ selectedLog.action }}</span>
        </div>
        <div class="detail-item">
          <label>IP地址：</label>
          <span>{{ selectedLog.ip || '-' }}</span>
        </div>
        <div class="detail-item">
          <label>消息：</label>
          <p class="detail-message">{{ selectedLog.message }}</p>
        </div>
        <div v-if="selectedLog.details" class="detail-item">
          <label>详细信息：</label>
          <pre class="detail-code">{{ selectedLog.details }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Warning, 
  CircleClose, 
  Tools, 
  Search, 
  Refresh, 
  Download 
} from '@element-plus/icons-vue'

// 日志类型标签页
const logTabs = [
  { key: 'all', label: '全部日志', icon: 'Document', count: 0 },
  { key: 'operation', label: '操作日志', icon: 'Tools', count: 0 },
  { key: 'error', label: '错误日志', icon: 'CircleClose', count: 0 },
  { key: 'security', label: '安全日志', icon: 'Warning', count: 0 }
]

const activeLogTab = ref('all')
const dateRange = ref([])
const selectedLevel = ref('')
const selectedModule = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const showDetailsDialog = ref(false)
const selectedLog = ref(null)

// 模拟日志数据
const logList = ref([
  {
    id: 1,
    timestamp: '2023-12-01 10:30:22',
    level: 'info',
    module: 'user',
    user: '张三',
    action: '用户登录',
    message: '用户张三成功登录系统',
    ip: '*************',
    details: null
  },
  {
    id: 2,
    timestamp: '2023-12-01 10:25:15',
    level: 'warning',
    module: 'auth',
    user: '李四',
    action: '密码错误',
    message: '用户李四登录密码错误，剩余尝试次数：2',
    ip: '*************',
    details: 'Login attempt failed for user: <EMAIL>'
  },
  {
    id: 3,
    timestamp: '2023-12-01 10:20:08',
    level: 'error',
    module: 'system',
    user: null,
    action: '数据库连接',
    message: '数据库连接超时',
    ip: null,
    details: 'Connection timeout after 30 seconds\nDatabase: mysql://localhost:3306/zhisuo\nError code: 1045'
  },
  {
    id: 4,
    timestamp: '2023-12-01 10:15:33',
    level: 'info',
    module: 'article',
    user: '王五',
    action: '文章发布',
    message: '用户王五发布了新文章《AI技术发展趋势》',
    ip: '*************',
    details: null
  },
  {
    id: 5,
    timestamp: '2023-12-01 10:10:45',
    level: 'debug',
    module: 'system',
    user: null,
    action: '系统启动',
    message: '系统服务启动完成',
    ip: null,
    details: 'All services started successfully\nMemory usage: 256MB\nCPU usage: 15%'
  }
])

// 过滤后的日志列表
const filteredLogs = computed(() => {
  let filtered = logList.value

  // 按标签页过滤
  if (activeLogTab.value !== 'all') {
    switch (activeLogTab.value) {
      case 'operation':
        filtered = filtered.filter(log => ['user', 'article'].includes(log.module))
        break
      case 'error':
        filtered = filtered.filter(log => log.level === 'error')
        break
      case 'security':
        filtered = filtered.filter(log => log.module === 'auth')
        break
    }
  }

  // 按级别过滤
  if (selectedLevel.value) {
    filtered = filtered.filter(log => log.level === selectedLevel.value)
  }

  // 按模块过滤
  if (selectedModule.value) {
    filtered = filtered.filter(log => log.module === selectedModule.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(keyword) ||
      log.action.toLowerCase().includes(keyword) ||
      (log.user && log.user.toLowerCase().includes(keyword))
    )
  }

  // 按时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startTime, endTime] = dateRange.value
    filtered = filtered.filter(log => 
      log.timestamp >= startTime && log.timestamp <= endTime
    )
  }

  totalCount.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.slice(start, start + pageSize.value)
})

// 格式化时间
const formatTime = (timestamp) => {
  return timestamp
}

// 获取级别标签类型
const getLevelTagType = (level) => {
  switch (level) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'primary'
    case 'debug': return 'info'
    default: return 'info'
  }
}

// 获取级别文本
const getLevelText = (level) => {
  switch (level) {
    case 'error': return '错误'
    case 'warning': return '警告'
    case 'info': return '信息'
    case 'debug': return '调试'
    default: return level
  }
}

// 获取模块文本
const getModuleText = (module) => {
  switch (module) {
    case 'user': return '用户管理'
    case 'article': return '文章管理'
    case 'system': return '系统设置'
    case 'auth': return '安全认证'
    default: return module
  }
}

// 显示日志详情
const showLogDetails = (log) => {
  selectedLog.value = log
  showDetailsDialog.value = true
}

// 刷新日志
const refreshLogs = () => {
  ElMessage.success('日志已刷新')
}

// 导出日志
const exportLogs = () => {
  ElMessage.info('正在导出日志...')
  // 模拟导出
  setTimeout(() => {
    ElMessage.success('日志导出成功')
  }, 1000)
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 更新标签页计数
const updateTabCounts = () => {
  logTabs[0].count = logList.value.length
  logTabs[1].count = logList.value.filter(log => ['user', 'article'].includes(log.module)).length
  logTabs[2].count = logList.value.filter(log => log.level === 'error').length
  logTabs[3].count = logList.value.filter(log => log.module === 'auth').length
}

onMounted(() => {
  totalCount.value = logList.value.length
  updateTabCounts()
})
</script>

<style lang="scss" scoped>
.system-logs {
  .logs-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }
    
    .page-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
  
  .log-tabs {
    margin-bottom: 24px;
    
    .tab-nav {
      display: flex;
      border-bottom: 1px solid #e9ecef;
      
      .tab-item {
        padding: 12px 20px;
        cursor: pointer;
        font-size: 14px;
        color: #6b7280;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 6px;
        position: relative;
        
        &:hover {
          color: #722ED1;
        }
        
        &.active {
          color: #722ED1;
          border-bottom-color: #722ED1;
          font-weight: 500;
        }
        
        .tab-badge {
          margin-left: 4px;
        }
      }
    }
  }
  
  .log-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;
    
    .filter-left,
    .filter-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
  
  .log-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    
    .log-time {
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
    }
    
    .log-module,
    .log-user,
    .log-action,
    .log-ip {
      font-size: 13px;
    }
    
    .log-message {
      .message-text {
        margin: 0 0 4px 0;
        font-size: 13px;
        line-height: 1.4;
      }
    }
    
    .pagination-wrapper {
      padding: 16px;
      display: flex;
      justify-content: center;
    }
  }
  
  .log-details {
    .detail-item {
      margin-bottom: 16px;
      
      label {
        font-weight: 500;
        color: #374151;
        display: inline-block;
        width: 80px;
      }
      
      .detail-message {
        margin: 4px 0 0 80px;
        line-height: 1.5;
      }
      
      .detail-code {
        margin: 4px 0 0 80px;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.4;
        overflow-x: auto;
      }
    }
  }
}
</style>
