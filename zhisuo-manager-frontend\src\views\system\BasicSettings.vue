<template>
  <div class="basic-settings">
    <div class="settings-section">
      <h3 class="section-title">系统信息</h3>
      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">系统名称</label>
          <el-input 
            v-model="systemInfo.name" 
            placeholder="请输入系统名称"
            class="setting-input"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">系统版本</label>
          <el-input 
            v-model="systemInfo.version" 
            placeholder="请输入系统版本"
            class="setting-input"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">系统描述</label>
          <el-input 
            v-model="systemInfo.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入系统描述"
            class="setting-input"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">联系邮箱</label>
          <el-input 
            v-model="systemInfo.email" 
            placeholder="请输入联系邮箱"
            class="setting-input"
          />
        </div>
      </div>
    </div>

    <div class="settings-section">
      <h3 class="section-title">界面设置</h3>
      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">主题色彩</label>
          <el-color-picker 
            v-model="uiSettings.primaryColor" 
            show-alpha
            class="setting-input"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">页面标题</label>
          <el-input 
            v-model="uiSettings.pageTitle" 
            placeholder="请输入页面标题"
            class="setting-input"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">Logo图片</label>
          <el-upload
            class="logo-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeLogoUpload"
          >
            <img v-if="uiSettings.logoUrl" :src="uiSettings.logoUrl" class="logo-preview" />
            <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">侧边栏折叠</label>
          <el-switch 
            v-model="uiSettings.sidebarCollapsed"
            active-text="默认折叠"
            inactive-text="默认展开"
          />
        </div>
      </div>
    </div>

    <div class="settings-section">
      <h3 class="section-title">功能设置</h3>
      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">用户注册</label>
          <el-switch 
            v-model="featureSettings.allowRegistration"
            active-text="允许"
            inactive-text="禁止"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">邮件通知</label>
          <el-switch 
            v-model="featureSettings.emailNotification"
            active-text="开启"
            inactive-text="关闭"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">数据备份</label>
          <el-switch 
            v-model="featureSettings.autoBackup"
            active-text="自动"
            inactive-text="手动"
          />
        </div>
        
        <div class="setting-item">
          <label class="setting-label">维护模式</label>
          <el-switch 
            v-model="featureSettings.maintenanceMode"
            active-text="开启"
            inactive-text="关闭"
          />
        </div>
      </div>
    </div>

    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings" :loading="saving">
        <el-icon><Check /></el-icon>
        保存设置
      </el-button>
      <el-button @click="resetSettings">
        <el-icon><RefreshLeft /></el-icon>
        重置设置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Check, RefreshLeft } from '@element-plus/icons-vue'

// 系统信息
const systemInfo = reactive({
  name: '智索管理平台',
  version: '1.0.0',
  description: '智能搜索与分析管理系统',
  email: '<EMAIL>'
})

// 界面设置
const uiSettings = reactive({
  primaryColor: '#722ED1',
  pageTitle: '智索管理平台',
  logoUrl: '',
  sidebarCollapsed: false
})

// 功能设置
const featureSettings = reactive({
  allowRegistration: true,
  emailNotification: true,
  autoBackup: false,
  maintenanceMode: false
})

const saving = ref(false)

// Logo上传前验证
const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('Logo图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('Logo图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  systemInfo.name = '智索管理平台'
  systemInfo.version = '1.0.0'
  systemInfo.description = '智能搜索与分析管理系统'
  systemInfo.email = '<EMAIL>'
  
  uiSettings.primaryColor = '#722ED1'
  uiSettings.pageTitle = '智索管理平台'
  uiSettings.logoUrl = ''
  uiSettings.sidebarCollapsed = false
  
  featureSettings.allowRegistration = true
  featureSettings.emailNotification = true
  featureSettings.autoBackup = false
  featureSettings.maintenanceMode = false
  
  ElMessage.success('设置已重置')
}
</script>

<style lang="scss" scoped>
.basic-settings {
  .settings-section {
    margin-bottom: 32px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #722ED1;
      display: inline-block;
    }
    
    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      
      .setting-item {
        display: flex;
        flex-direction: column;
        
        .setting-label {
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
        
        .setting-input {
          width: 100%;
        }
      }
    }
  }
  
  .logo-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        border-color: #722ED1;
      }
    }
    
    .logo-uploader-icon {
      font-size: 28px;
      color: #8c939d;
    }
    
    .logo-preview {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .settings-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
  }
}
</style>
