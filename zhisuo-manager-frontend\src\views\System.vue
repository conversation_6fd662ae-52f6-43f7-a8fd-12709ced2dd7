<template>
  <div class="system-page">
    <div class="page-header">
      <h2>系统设置</h2>
      <p class="page-desc">管理系统管理员、操作日志和系统配置</p>
    </div>

    <!-- 系统管理员管理 -->
    <div class="content-card">
      <div class="section-header">
        <div class="section-title">
          <el-icon><UserFilled /></el-icon>
          <span>系统管理员</span>
        </div>
        <div class="section-actions">
          <el-button type="primary" @click="handleAddAdmin">
            <el-icon><Plus /></el-icon>
            添加管理员
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-container">
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="用户名">
              <el-input
                v-model="searchForm.username"
                placeholder="请输入用户名"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="角色">
              <el-select
                v-model="searchForm.role"
                placeholder="请选择角色"
                clearable
                style="width: 150px"
              >
                <el-option label="超级管理员" value="admin" />
                <el-option label="普通管理员" value="manager" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择状态"
                clearable
                style="width: 120px"
              >
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleResetSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 管理员列表 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="adminList"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="username" label="用户名" width="150" />
          <el-table-column prop="realName" label="真实姓名" width="120" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="role" label="角色" width="120">
            <template #default="{ row }">
              <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
                {{ row.role === 'admin' ? '超级管理员' : '普通管理员' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginTime" label="最后登录" width="160">
            <template #default="{ row }">
              {{ row.lastLoginTime ? formatDateTime(row.lastLoginTime) : '从未登录' }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEditAdmin(row)">
                编辑
              </el-button>
              <el-button
                :type="row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(row)"
                :disabled="row.username === 'admin'"
              >
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDeleteAdmin(row)"
                :disabled="row.username === 'admin'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 添加管理员对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加管理员"
      width="500px"
      :before-close="handleCloseAdd"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="addForm.username"
            placeholder="请输入用户名"
            maxlength="20"
            clearable
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="addForm.password"
            type="password"
            placeholder="请输入密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="addForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="addForm.realName"
            placeholder="请输入真实姓名"
            maxlength="20"
            clearable
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="addForm.email"
            placeholder="请输入邮箱"
            maxlength="50"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="addForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
            clearable
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="addForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="普通管理员" value="manager" />
            <el-option label="超级管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseAdd">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="handleConfirmAdd">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑管理员对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑管理员"
      width="500px"
      :before-close="handleCloseEdit"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="editForm.password"
            type="password"
            placeholder="留空则不修改密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="editForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="editForm.realName"
            placeholder="请输入真实姓名"
            maxlength="20"
            clearable
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="editForm.email"
            placeholder="请输入邮箱"
            maxlength="50"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="editForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
            clearable
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="editForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="普通管理员" value="manager" />
            <el-option label="超级管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseEdit">取消</el-button>
          <el-button type="primary" :loading="editLoading" @click="handleConfirmEdit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  UserFilled
} from '@element-plus/icons-vue'
import { systemApi } from '@/api/system'
import { formatDateTime } from '@/utils/dateFormat'

// 响应式数据
const loading = ref(false)
const adminList = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 添加管理员相关
const addDialogVisible = ref(false)
const addLoading = ref(false)
const addFormRef = ref(null)
const addForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  email: '',
  phone: '',
  role: 'manager'
})

// 编辑管理员相关
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  adminId: '',
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  email: '',
  phone: '',
  role: 'manager'
})

// 表单验证规则
const addFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== addForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 20, message: '真实姓名不能超过20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const editFormRules = {
  password: [
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (editForm.password && value !== editForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 20, message: '真实姓名不能超过20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取管理员列表
const fetchAdminList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      size: pagination.size,
      ...searchForm
    }

    const response = await systemApi.getAdminList(params)

    if (response.code === 200) {
      adminList.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取管理员列表失败')
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchAdminList()
}

// 重置搜索
const handleResetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    role: '',
    status: ''
  })
  pagination.current = 1
  fetchAdminList()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchAdminList()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchAdminList()
}

// 添加管理员
const handleAddAdmin = () => {
  addDialogVisible.value = true
}

const handleCloseAdd = () => {
  addDialogVisible.value = false
  resetAddForm()
}

const resetAddForm = () => {
  Object.assign(addForm, {
    username: '',
    password: '',
    confirmPassword: '',
    realName: '',
    email: '',
    phone: '',
    role: 'manager'
  })
  addFormRef.value?.clearValidate()
}

const handleConfirmAdd = async () => {
  try {
    await addFormRef.value.validate()

    addLoading.value = true

    const response = await systemApi.createAdmin({
      username: addForm.username,
      password: addForm.password,
      realName: addForm.realName,
      email: addForm.email,
      phone: addForm.phone,
      role: addForm.role
    })

    if (response.code === 200) {
      ElMessage.success('添加管理员成功')
      addDialogVisible.value = false
      resetAddForm()
      fetchAdminList()
    } else {
      ElMessage.error(response.message || '添加管理员失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('添加管理员失败:', error)
      ElMessage.error('添加管理员失败')
    }
  } finally {
    addLoading.value = false
  }
}

// 编辑管理员
const handleEditAdmin = (admin) => {
  Object.assign(editForm, {
    adminId: admin.adminId,
    username: admin.username,
    password: '',
    confirmPassword: '',
    realName: admin.realName,
    email: admin.email,
    phone: admin.phone,
    role: admin.role
  })
  editDialogVisible.value = true
}

const handleCloseEdit = () => {
  editDialogVisible.value = false
  resetEditForm()
}

const resetEditForm = () => {
  Object.assign(editForm, {
    adminId: '',
    username: '',
    password: '',
    confirmPassword: '',
    realName: '',
    email: '',
    phone: '',
    role: 'manager'
  })
  editFormRef.value?.clearValidate()
}

const handleConfirmEdit = async () => {
  try {
    await editFormRef.value.validate()

    editLoading.value = true

    const updateData = {
      adminId: editForm.adminId,
      realName: editForm.realName,
      email: editForm.email,
      phone: editForm.phone,
      role: editForm.role
    }

    // 如果密码不为空，则包含密码
    if (editForm.password && editForm.password.trim()) {
      updateData.password = editForm.password
    }

    const response = await systemApi.updateAdmin(updateData)

    if (response.code === 200) {
      ElMessage.success('更新管理员成功')
      editDialogVisible.value = false
      resetEditForm()
      fetchAdminList()
    } else {
      ElMessage.error(response.message || '更新管理员失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新管理员失败:', error)
      ElMessage.error('更新管理员失败')
    }
  } finally {
    editLoading.value = false
  }
}

// 切换状态
const handleToggleStatus = async (admin) => {
  const action = admin.status === 1 ? '禁用' : '启用'
  const newStatus = admin.status === 1 ? 0 : 1

  try {
    await ElMessageBox.confirm(
      `确定要${action}管理员 "${admin.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await systemApi.updateAdminStatus({
      adminId: admin.adminId,
      status: newStatus
    })

    if (response.code === 200) {
      ElMessage.success(`${action}成功`)
      fetchAdminList()
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}管理员失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除管理员
const handleDeleteAdmin = async (admin) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除管理员 "${admin.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await systemApi.deleteAdmin(admin.adminId)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      fetchAdminList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除管理员失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 页面初始化
onMounted(() => {
  fetchAdminList()
})
</script>

<style lang="scss" scoped>
.system-page {
  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .page-desc {
      color: #666;
      margin: 0;
      font-size: 14px;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #E5E7EB;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;

      .el-icon {
        color: #3B82F6;
      }
    }

    .section-actions {
      display: flex;
      gap: 12px;
    }
  }

  .search-container {
    margin-bottom: 20px;
    padding: 20px;
    background: #F9FAFB;
    border-radius: 8px;

    .search-form {
      .el-form {
        margin: 0;

        .el-form-item {
          margin-bottom: 0;
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .table-container {
    .el-table {
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #E5E7EB;

      .el-table__header {
        background-color: #F9FAFB;

        th {
          background-color: #F9FAFB !important;
          color: #374151;
          font-weight: 600;
          border-bottom: 1px solid #E5E7EB;
        }
      }

      .el-table__row {
        &:hover {
          background-color: #F9FAFB;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-footer {
    text-align: center;

    .el-button {
      min-width: 80px;
    }
  }
}

// 全局样式覆盖
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #E5E7EB;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #E5E7EB;
  }
}

:deep(.el-form) {
  .el-form-item__label {
    color: #374151;
    font-weight: 500;
  }

  .el-input__wrapper {
    border-radius: 6px;
  }

  .el-select .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;

  &.el-button--primary {
    background-color: #3B82F6;
    border-color: #3B82F6;

    &:hover {
      background-color: #2563EB;
      border-color: #2563EB;
    }
  }

  &.el-button--success {
    background-color: #10B981;
    border-color: #10B981;

    &:hover {
      background-color: #059669;
      border-color: #059669;
    }
  }

  &.el-button--warning {
    background-color: #F59E0B;
    border-color: #F59E0B;

    &:hover {
      background-color: #D97706;
      border-color: #D97706;
    }
  }

  &.el-button--danger {
    background-color: #EF4444;
    border-color: #EF4444;

    &:hover {
      background-color: #DC2626;
      border-color: #DC2626;
    }
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}
</style>
