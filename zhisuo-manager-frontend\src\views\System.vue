<template>
  <div class="system-page">
    <div class="page-header">
      <h2>系统设置</h2>
      <p class="page-description">管理系统的基本配置、用户权限、安全设置和系统日志</p>
    </div>

    <div class="content-card">
      <el-tabs v-model="activeTab" class="system-tabs" @tab-click="handleTabClick">
        <el-tab-pane label="基本设置" name="basic">
          <BasicSettings />
        </el-tab-pane>

        <el-tab-pane label="管理员与权限" name="admin">
          <AdminPermissions />
        </el-tab-pane>

        <el-tab-pane label="安全配置" name="security">
          <SecurityConfig />
        </el-tab-pane>

        <el-tab-pane label="系统日志" name="logs">
          <SystemLogs />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BasicSettings from './system/BasicSettings.vue'
import AdminPermissions from './system/AdminPermissions.vue'
import SecurityConfig from './system/SecurityConfig.vue'
import SystemLogs from './system/SystemLogs.vue'

// 当前激活的标签页
const activeTab = ref('basic')

// 处理标签页切换
const handleTabClick = (tab) => {
  console.log('切换到标签页:', tab.props.name)
}
</script>

<style lang="scss" scoped>
.system-page {
  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .page-description {
      color: #666;
      font-size: 14px;
      margin: 0;
      line-height: 1.5;
    }
  }

  .content-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .system-tabs {
    :deep(.el-tabs__header) {
      margin: 0;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 0 24px;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        display: none;
      }
    }

    :deep(.el-tabs__item) {
      height: 56px;
      line-height: 56px;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 500;
      color: #6b7280;
      border: none;

      &:hover {
        color: #722ED1;
      }

      &.is-active {
        color: #722ED1;
        background: #fff;
        border-radius: 8px 8px 0 0;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: #722ED1;
        }
      }
    }

    :deep(.el-tabs__content) {
      padding: 24px;
      min-height: 500px;
    }
  }
}
</style>
